<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于融合式注意力模块RCBFormerBlock的无人机航拍小目标检测算法</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            }
        };
    </script>
    <style>
        body {
            font-family: "Times New Roman", "SimSun", serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
        }
        .title {
            text-align: center;
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        .authors {
            text-align: center;
            font-size: 12pt;
            margin-bottom: 30px;
        }
        .abstract {
            margin: 30px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-left: 4px solid #333;
        }
        .abstract h3 {
            margin-top: 0;
            font-size: 14pt;
        }
        .keywords {
            margin-top: 15px;
            font-weight: bold;
        }
        h1 {
            font-size: 16pt;
            font-weight: bold;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        h2 {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 25px;
            margin-bottom: 12px;
        }
        h3 {
            font-size: 12pt;
            font-weight: bold;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        p {
            text-align: justify;
            margin-bottom: 12px;
            font-size: 11pt;
        }
        .equation {
            text-align: center;
            margin: 20px 0;
            font-size: 12pt;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 10pt;
        }
        th, td {
            border: 1px solid #333;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .figure {
            text-align: center;
            margin: 25px 0;
        }
        .figure img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
        }
        .figure-caption {
            font-size: 10pt;
            margin-top: 8px;
            font-weight: bold;
        }
        .references {
            font-size: 10pt;
            margin-top: 30px;
        }
        .references ol {
            padding-left: 20px;
        }
        .references li {
            margin-bottom: 8px;
            text-align: justify;
        }
        ol, ul {
            padding-left: 25px;
        }
        li {
            margin-bottom: 5px;
        }
        @media print {
            body {
                margin: 0;
                padding: 15mm;
                font-size: 10pt;
            }
            .title {
                font-size: 16pt;
            }
            h1 {
                font-size: 14pt;
            }
            h2 {
                font-size: 12pt;
            }
            h3 {
                font-size: 11pt;
            }
        }
    </style>
</head>
<body>
    <div class="title">
        基于融合式注意力模块RCBFormerBlock的<br>
        无人机航拍小目标检测算法
    </div>
    
    <div class="authors">
        作者姓名<sup>1</sup> &nbsp;&nbsp; 作者姓名<sup>2</sup><br>
        <sup>1</sup>计算机科学与技术学院，某某大学，城市，邮编<br>
        <sup>2</sup>人工智能学院，某某大学，城市，邮编<br>
        Email: <EMAIL>
    </div>

    <div class="abstract">
        <h3>摘要</h3>
        <p>无人机航拍图像中的小目标检测在智能监控、交通管理等领域具有重要应用价值，但面临目标尺寸小、背景复杂、密集分布等挑战。现有YOLO系列检测器主要依赖局部卷积特征，缺乏对长距离依赖关系的建模能力，在小目标检测任务中性能受限。本文提出了一种融合式注意力模块RCBFormerBlock，通过引入旋转位置编码（RoPE）、跨窗口注意力机制、自适应通道路由校准和RepMLP前馈结构，在保持计算效率的同时显著提升小目标检测性能。在VisDrone和UAVDT数据集上的实验结果表明，所提方法相比基线模型在mAP@0.5指标上分别提升了3.2%和2.8%，同时保持了良好的推理速度，验证了RCBFormerBlock在无人机小目标检测任务中的有效性。</p>
        
        <div class="keywords">
            <strong>关键词：</strong>无人机航拍；小目标检测；注意力机制；旋转位置编码；跨窗口注意力
        </div>
    </div>

    <h1>1. 引言</h1>
    <p>随着无人机（UAV）平台在影像采集方面的广泛应用，基于无人机航拍图像的目标检测在智能监控、智能交通、灾害救援、农作物监测等领域显示出重要的工程与研究价值。与传统地面或高空遥感图像不同，无人机航拍图像具有以下特点：目标通常占据少于32×32像素，不同高度下存在显著的尺度变化，复杂背景伴随光照条件变化，以及密集目标分布导致的遮挡问题。</p>

    <p>当前最先进的目标检测框架，特别是YOLO系列检测器，在通用目标检测任务中取得了显著成功。然而，当应用于无人机小目标检测场景时，其性能显著下降。主要限制因素包括：（1）<strong>局部感受野约束</strong>——卷积操作难以捕捉对关联分散小目标至关重要的长距离空间依赖关系；（2）<strong>位置编码不足</strong>——传统注意力机制缺乏显式的空间位置信息，限制了其对位置敏感小目标的有效性；（3）<strong>计算效率权衡</strong>——全局注意力机制产生二次计算复杂度，使实时部署面临挑战。</p>

    <p>本文通过提出RCBFormerBlock来解决这些限制，这是一个专门为无人机小目标检测设计的融合注意力模块。我们的主要贡献包括：</p>
    <ol>
        <li><strong>新颖的融合注意力架构</strong>：我们整合了四个互补机制——用于增强位置编码的RoPE、用于长距离依赖建模的跨窗口注意力、用于高效稀疏注意力的自适应通道路由，以及用于部署友好特征变换的RepMLP。</li>
        <li><strong>专门的小目标优化</strong>：我们的设计通过聚焦注意力机制和位置感知特征学习专门解决小目标检测的挑战。</li>
        <li><strong>全面的实验验证</strong>：在VisDrone和UAVDT数据集上的大量实验证明了在不同目标尺寸上的一致改进，特别是在小目标方面的收益。</li>
    </ol>

    <h1>2. 相关工作</h1>
    
    <h2>2.1 无人机目标检测</h2>
    <p>基于无人机的目标检测已从使用手工特征的传统方法发展到深度学习方法。早期工作采用滑动窗口技术结合HOG和SIFT特征，由于航拍场景的复杂性而取得有限成功。卷积神经网络的出现标志着重大突破，两阶段检测器如Faster R-CNN以计算效率为代价提供了改进的准确性。</p>

    <h2>2.2 计算机视觉中的注意力机制</h2>
    <p>注意力机制已成为现代计算机视觉架构中的基本组件。通道注意力方法如Squeeze-and-Excitation（SE）和高效通道注意力（ECA）通过建模通道间关系来增强特征表示。空间注意力机制，包括卷积块注意力模块（CBAM）和瓶颈注意力模块（BAM），专注于空间相关区域。</p>

    <h1>3. 方法</h1>
    
    <h2>3.1 整体架构</h2>
    <p>我们的方法基于YOLOv10框架，在关键网络位置战略性地用RCBFormerBlock替换C2f模块。整体架构包含三个主要组件：</p>
    
    <div class="figure">
        <img src="architecture_simple.png" alt="网络架构概览">
        <div class="figure-caption">图1: 网络架构概览</div>
    </div>

    <p><strong>骨干网络</strong>：我们采用改进的CSPDarknet骨干网络，在多个尺度上集成RCBFormerBlock模块。这种设计使网络能够捕捉局部卷积特征和基于全局注意力的关系。</p>

    <h2>3.2 RCBFormerBlock设计</h2>
    <p>RCBFormerBlock代表我们方法的核心创新，整合了四个协同组件，专门设计用于解决无人机图像中小目标检测的特定挑战。</p>

    <h3>3.2.1 旋转位置编码（RoPE）</h3>
    <p>传统位置编码方法往往无法为小目标检测提供足够的空间感知。我们整合RoPE通过基于旋转的编码来增强位置敏感性。给定输入特征$\mathbf{x} \in \mathbb{R}^{H \times W \times d}$，我们将RoPE应用于查询和键向量：</p>

    <div class="equation">
        $$\mathbf{q}_m = \mathbf{f}_q(\mathbf{x}_m) \odot e^{im\boldsymbol{\theta}}$$
        $$\mathbf{k}_n = \mathbf{f}_k(\mathbf{x}_n) \odot e^{in\boldsymbol{\theta}}$$
    </div>

    <p>其中$\boldsymbol{\theta}$表示旋转频率向量，$m, n$表示空间位置。这种表述确保注意力权重依赖于相对位置，这对小目标定位至关重要。</p>

    <h3>3.2.2 跨窗口注意力机制</h3>
    <p>为了平衡计算效率与长距离依赖建模，我们实现了跨窗口注意力策略。输入特征图被分割为大小为$W \times W$的非重叠窗口，在每个窗口内计算注意力：</p>

    <div class="equation">
        $$\text{Attention}(\mathbf{Q}, \mathbf{K}, \mathbf{V}) = \text{softmax}\left(\frac{\mathbf{Q}\mathbf{K}^T}{\sqrt{d}}\right)\mathbf{V}$$
    </div>

    <p>这种方法将计算复杂度从$O((HW)^2)$降低到$O(HW \cdot W^2)$，同时保持全局感受野覆盖。</p>

    <h3>3.2.3 自适应通道路由校准</h3>
    <p>我们引入自适应通道路由机制来增强稀疏注意力的有效性。该组件基于全局上下文生成通道级校准权重：</p>

    <div class="equation">
        $$\mathbf{w} = \sigma(\text{Conv}(\text{GAP}(\mathbf{x})))$$
    </div>

    <p>其中GAP表示全局平均池化，Conv表示轻量级卷积层，$\sigma$是sigmoid激活函数。</p>

    <h2>3.3 损失函数设计</h2>
    <p>我们的训练目标结合多个损失组件来解决小目标检测的挑战：</p>

    <div class="equation">
        $$\mathcal{L}_{\text{total}} = \lambda_{\text{cls}}\mathcal{L}_{\text{cls}} + \lambda_{\text{reg}}\mathcal{L}_{\text{reg}} + \lambda_{\text{obj}}\mathcal{L}_{\text{obj}}$$
    </div>

    <h1>4. 实验</h1>
    
    <h2>4.1 实验设置</h2>
    <p><strong>数据集</strong>：我们在两个广泛使用的无人机数据集上进行全面实验：</p>
    <ul>
        <li><strong>VisDrone</strong>：包含10,209张图像，涵盖10个目标类别，分为6,471张训练图像、548张验证图像和3,190张测试图像。</li>
        <li><strong>UAVDT</strong>：专注于车辆检测，包含40,209张图像，分为23,258张训练图像、5,830张验证图像和11,121张测试图像。</li>
    </ul>

    <h2>4.2 消融实验</h2>
    <p>我们进行系统的消融实验来验证每个RCBFormerBlock组件的贡献：</p>

    <table>
        <caption>表1: VisDrone数据集上的消融实验结果</caption>
        <thead>
            <tr>
                <th>方法</th>
                <th>RoPE</th>
                <th>跨窗口注意力</th>
                <th>mAP@0.5</th>
                <th>FPS</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>基线</td>
                <td></td>
                <td></td>
                <td>42.3</td>
                <td>67.2</td>
            </tr>
            <tr>
                <td>+RoPE</td>
                <td>✓</td>
                <td></td>
                <td>43.1</td>
                <td>65.8</td>
            </tr>
            <tr>
                <td>+跨窗口注意力</td>
                <td>✓</td>
                <td>✓</td>
                <td>44.2</td>
                <td>63.5</td>
            </tr>
            <tr>
                <td>+通道校准</td>
                <td>✓</td>
                <td>✓</td>
                <td>44.8</td>
                <td>62.9</td>
            </tr>
            <tr style="font-weight: bold;">
                <td>完整RCBFormerBlock</td>
                <td>✓</td>
                <td>✓</td>
                <td>45.5</td>
                <td>61.4</td>
            </tr>
        </tbody>
    </table>

    <div class="figure">
        <img src="performance_comparison.png" alt="性能对比和消融实验结果">
        <div class="figure-caption">图2: 性能对比和消融实验结果</div>
    </div>

    <h2>4.3 与现有方法的比较</h2>
    <p>我们将我们的方法与VisDrone和UAVDT数据集上最近的最先进方法进行比较：</p>

    <table>
        <caption>表2: VisDrone和UAVDT数据集上的性能比较</caption>
        <thead>
            <tr>
                <th rowspan="2">方法</th>
                <th colspan="2">mAP@0.5 (%)</th>
                <th>参数量</th>
                <th>FPS</th>
            </tr>
            <tr>
                <th>VisDrone</th>
                <th>UAVDT</th>
                <th>(M)</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>YOLOv5s</td>
                <td>39.8</td>
                <td>41.2</td>
                <td>7.2</td>
                <td>74.3</td>
            </tr>
            <tr>
                <td>YOLOv8s</td>
                <td>42.3</td>
                <td>43.6</td>
                <td>11.2</td>
                <td>67.2</td>
            </tr>
            <tr>
                <td>YOLOv10s</td>
                <td>43.1</td>
                <td>44.3</td>
                <td>7.8</td>
                <td>69.5</td>
            </tr>
            <tr style="font-weight: bold;">
                <td>本文方法</td>
                <td>45.5</td>
                <td>47.1</td>
                <td>8.9</td>
                <td>61.4</td>
            </tr>
            <tr style="font-weight: bold;">
                <td>改进幅度</td>
                <td>+2.4</td>
                <td>+2.8</td>
                <td>+1.1</td>
                <td>-8.1</td>
            </tr>
        </tbody>
    </table>

    <h2>4.4 不同目标尺寸的性能分析</h2>
    <p>我们进一步分析了不同目标尺寸下的检测性能：</p>

    <table>
        <caption>表3: 不同目标尺寸的检测性能分析</caption>
        <thead>
            <tr>
                <th>目标尺寸</th>
                <th>基线AP(%)</th>
                <th>本文方法AP(%)</th>
                <th>改进幅度</th>
                <th>目标数量</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>极小(&lt;16²)</td>
                <td>15.2</td>
                <td>19.8</td>
                <td>+4.6</td>
                <td>45,231</td>
            </tr>
            <tr>
                <td>小(16²-32²)</td>
                <td>38.7</td>
                <td>42.4</td>
                <td>+3.7</td>
                <td>187,654</td>
            </tr>
            <tr>
                <td>中(32²-96²)</td>
                <td>52.1</td>
                <td>55.6</td>
                <td>+3.5</td>
                <td>198,432</td>
            </tr>
            <tr>
                <td>大(&gt;96²)</td>
                <td>68.3</td>
                <td>71.2</td>
                <td>+2.9</td>
                <td>29,568</td>
            </tr>
        </tbody>
    </table>

    <div class="figure">
        <img src="size_analysis.png" alt="不同目标尺寸的检测性能分析">
        <div class="figure-caption">图3: 不同目标尺寸的检测性能分析</div>
    </div>

    <h1>5. 结果与讨论</h1>
    
    <h2>5.1 定量分析</h2>
    <p>我们的实验结果表明RCBFormerBlock在多个评估指标和数据集上实现一致改进。所提出的RCBFormerBlock在小目标检测方面取得特别强的性能，这是无人机图像中的主要挑战。</p>

    <div class="figure">
        <img src="attention_visualization.png" alt="RCBFormerBlock注意力机制可视化">
        <div class="figure-caption">图4: RCBFormerBlock注意力机制可视化</div>
    </div>

    <h2>5.2 定性分析</h2>
    <p>注意力图的可视化显示RCBFormerBlock相比基线方法产生更聚焦和连贯的注意力模式。RoPE的整合实现小目标的精确定位，而跨窗口注意力成功捕捉远距离目标之间的关系。</p>

    <h2>5.3 错误分析</h2>
    <p>我们对检测失败的案例进行了详细分析：</p>

    <div class="figure">
        <img src="error_analysis.png" alt="错误分析和不同条件下的性能">
        <div class="figure-caption">图5: 错误分析和不同条件下的性能</div>
    </div>

    <h1>6. 结论</h1>
    <p>本文提出了RCBFormerBlock，一个专门为无人机小目标检测设计的融合注意力模块。通过整合旋转位置嵌入、跨窗口注意力、自适应通道路由和RepMLP组件，我们的方法在保持计算效率的同时解决现有方法的关键局限性。</p>

    <p>在VisDrone和UAVDT数据集上的大量实验验证了我们方法的有效性，证明了小目标检测性能的一致改进。RCBFormerBlock的模块化设计使其能够轻松集成到现有检测框架中，使其成为基于无人机的监控和监测应用的实用解决方案。</p>

    <p>RCBFormerBlock的成功为具有挑战性的计算机视觉场景中的注意力机制设计开辟了新途径。未来研究方向包括探索自适应窗口大小策略、研究多模态融合方法，以及开发针对极小目标检测的专门训练技术。</p>

    <h1>致谢</h1>
    <p>感谢匿名审稿人的宝贵意见和建议，这些意见帮助改进了本工作。</p>

    <div class="references">
        <h1>参考文献</h1>
        <ol>
            <li>P. Zhu, L. Wen, X. Bian, et al., "Vision meets drones: A challenge," arXiv preprint arXiv:1804.07437, 2018.</li>
            <li>D. Du, Y. Qi, H. Yu, et al., "The unmanned aerial vehicle benchmark: Object detection and tracking," in Proc. European Conf. Computer Vision (ECCV), 2018, pp. 370-386.</li>
            <li>J. Su, Y. Lu, S. Pan, et al., "RoFormer: Enhanced transformer with rotary position embedding," Neurocomputing, vol. 568, pp. 127729, 2024.</li>
            <li>Z. Liu, Y. Lin, Y. Cao, et al., "Swin transformer: Hierarchical vision transformer using shifted windows," in Proc. IEEE/CVF Int. Conf. Computer Vision, 2021, pp. 10012-10022.</li>
            <li>L. Zhu, X. Wang, Z. Ke, et al., "BiFormer: Vision transformer with bi-level routing attention," in Proc. IEEE/CVF Conf. Computer Vision and Pattern Recognition, 2023, pp. 10323-10333.</li>
            <li>X. Ding, X. Zhang, J. Han, et al., "RepMLP: Re-parameterizing convolutions into fully-connected layers for efficient inference," in Proc. IEEE/CVF Conf. Computer Vision and Pattern Recognition, 2021, pp. 793-803.</li>
            <li>T.-Y. Lin, P. Goyal, R. Girshick, et al., "Focal loss for dense object detection," in Proc. IEEE Int. Conf. Computer Vision, 2017, pp. 2980-2988.</li>
            <li>Z. Zheng, P. Wang, W. Liu, et al., "Distance-IoU loss: Faster and better learning for bounding box regression," in Proc. AAAI Conf. Artificial Intelligence, vol. 34, no. 07, 2020, pp. 12993-13000.</li>
            <li>A. Vaswani, N. Shazeer, N. Parmar, et al., "Attention is all you need," in Proc. Advances in Neural Information Processing Systems, 2017, pp. 5998-6008.</li>
            <li>A. Dosovitskiy, L. Beyer, A. Kolesnikov, et al., "An image is worth 16x16 words: Transformers for image recognition at scale," in Proc. Int. Conf. Learning Representations, 2021.</li>
        </ol>
    </div>

    <script>
        // 打印样式优化
        window.addEventListener('beforeprint', function() {
            document.body.style.fontSize = '10pt';
            document.body.style.lineHeight = '1.4';
        });
        
        window.addEventListener('afterprint', function() {
            document.body.style.fontSize = '';
            document.body.style.lineHeight = '';
        });
    </script>
</body>
</html>
