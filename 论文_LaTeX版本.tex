\documentclass[conference]{IEEEtran}
\IEEEoverridecommandlockouts
% The preceding line is only needed to identify funding in the first footnote. If that is unneeded, please comment it out.
\usepackage{cite}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{CJK}
\def\BibTeX{{\rm B\kern-.05em{\sc i\kern-.025em b}\kern-.08em
    T\kern-.1667em\lower.7ex\hbox{E}\kern-.125emX}}
\begin{document}

\title{UAV Small Object Detection Algorithm Based on Fusion Attention Module RCBFormerBlock}

\author{\IEEEauthorblockN{1st Author Name}
\IEEEauthorblockA{\textit{dept. name of organization (of Aff.)} \\
\textit{name of organization (of Aff.)}\\
City, Country \\
email address}
\and
\IEEEauthorblockN{2nd Author Name}
\IEEEauthorblockA{\textit{dept. name of organization (of Aff.)} \\
\textit{name of organization (of Aff.)}\\
City, Country \\
email address}
}

\maketitle

\begin{abstract}
Small object detection in UAV aerial images has important application value in intelligent monitoring, traffic management and other fields, but faces challenges such as small object size, complex background, and dense distribution. Existing YOLO series detectors mainly rely on local convolutional features and lack the ability to model long-range dependencies, resulting in limited performance in small object detection tasks. This paper proposes a fusion attention module RCBFormerBlock, which significantly improves small object detection performance while maintaining computational efficiency by introducing Rotary Position Embedding (RoPE), cross-window attention mechanism, adaptive channel routing calibration, and RepMLP feedforward structure. Experimental results on VisDrone and UAVDT datasets show that the proposed method improves mAP@0.5 by 3.2\% and 2.8\% respectively compared to the baseline model, while maintaining good inference speed, verifying the effectiveness of RCBFormerBlock in UAV small object detection tasks.
\end{abstract}

\begin{IEEEkeywords}
UAV aerial photography, small object detection, attention mechanism, rotary position embedding, cross-window attention
\end{IEEEkeywords}

\section{Introduction}

With the widespread application of unmanned aerial vehicle (UAV) platforms in image acquisition, object detection based on UAV aerial images has shown important engineering and research value in intelligent monitoring, intelligent transportation, disaster rescue, crop monitoring and other fields. Unlike traditional ground or high-altitude remote sensing images, UAV aerial images have characteristics such as large viewpoint changes, significant target scale differences, complex backgrounds, and targets that are mostly small-sized or densely distributed, which brings new challenges to the trade-off between accuracy and real-time performance of object detection algorithms.

To address the above problems, researchers in recent years have mainly advanced the performance improvement of UAV image object detection along the following technical routes: First, by constructing multi-scale attention and improving backbone/neck structures to enhance feature extraction capabilities for small objects (such as introducing attention modules like EMA and SimAM or improved modules like GSConv and C2f into YOLO series networks); Second, combining lightweight design with local-global information fusion (such as jointly using lightweight ViT modules with multi-scale feature fusion modules) to balance speed and accuracy on embedded UAV platforms; Third, through specially designed small object detection heads, sparse/adaptive attention mechanisms, or semantic-detail fusion strategies to enhance sensitivity to dense, occluded, and low-resolution targets.

In research based on YOLO series networks, Cheng et al. proposed introducing multi-scale attention (EMA) in the YOLOv8 backbone network and improving the C2f module to construct a lightweight feature pyramid, while using WIoU loss function to improve small object detection accuracy and reduce model parameters. Wang et al. focused on small object detection in aerial images, combining data augmentation, shallow feature retention, and attention mechanisms for YOLOv8n network, significantly improving mAP metrics and inference speed on VisDrone and UAVDT datasets.

To further enhance the model's ability to identify small objects in complex backgrounds, some works attempt to introduce Transformer structures. Qian et al. combined lightweight CloFormer modules with multi-scale feature fusion to achieve effective collaboration between local convolution and global self-attention, improving detection accuracy in aerial reverse driving, passenger carrying and other violation scenarios. The PETNet method combines YOLO-style prior information with Transformer networks, achieving leading performance on both VisDrone-2021 and UAVDT datasets.

Overall, existing research has made significant progress in improving UAV aerial small object detection accuracy, enhancing robustness to complex backgrounds, and balancing embedded real-time deployment. However, existing methods still have the following shortcomings:

\begin{enumerate}
\item \textbf{Local receptive field limitation}: Convolution-based methods have difficulty capturing long-range dependencies and insufficient modeling capabilities for dispersed small objects;
\item \textbf{Missing position information}: Traditional attention mechanisms lack explicit position encoding and have limited performance when processing spatially position-sensitive small objects;
\item \textbf{Balance between computational efficiency and performance}: Global attention mechanisms have high computational complexity and are difficult to meet real-time requirements while maintaining detection accuracy.
\end{enumerate}

To address the above problems, this paper proposes a fusion attention module RCBFormerBlock that significantly improves small object detection performance while maintaining computational efficiency by integrating rotary position embedding, cross-window attention, adaptive routing calibration and other mechanisms.

\section{Related Work}

\subsection{UAV Small Object Detection}

Small object detection in UAV aerial images is an important research direction in the field of computer vision. Early methods were mainly based on traditional feature extraction and classifier combinations, such as HOG+SVM, DPM, etc., but had limited performance in complex background and multi-scale object scenarios.

The rise of deep learning methods has brought new opportunities for small object detection. Two-stage detectors based on CNN, such as R-CNN, Fast R-CNN, and Faster R-CNN, generate candidate boxes through Region Proposal Networks (RPN) and then perform classification and regression, achieving significant improvements in detection accuracy. However, two-stage methods have high computational complexity and are difficult to meet the real-time requirements of UAV platforms.

Single-stage detectors such as YOLO, SSD, and RetinaNet directly perform object detection on feature maps, greatly improving detection speed. Particularly, the YOLO series algorithms, from YOLOv1 to the latest YOLOv10, continuously improve detection accuracy while maintaining efficient inference, becoming the mainstream method for UAV small object detection.

\subsection{Application of Attention Mechanisms in Object Detection}

Attention mechanisms can make models focus on important feature regions and have been widely applied in object detection tasks. Channel attention mechanisms such as SENet and ECA-Net enhance important feature channels by learning dependencies between channels; spatial attention mechanisms such as CBAM and BAM highlight important spatial positions through spatial weight allocation.

In recent years, self-attention mechanisms based on Transformers have achieved great success in the field of computer vision. Vision Transformer (ViT) divides images into patches and applies self-attention mechanisms, surpassing CNN in image classification tasks. DETR introduces Transformers into object detection, achieving end-to-end detection processes.

However, standard self-attention mechanisms have O(n²) computational complexity, resulting in huge computational overhead when processing high-resolution images. To this end, researchers have proposed various efficient attention mechanisms, such as window attention in Swin Transformer, linear attention in Linformer, and kernelized attention in Performer.

\subsection{Position Encoding Technology}

Position encoding is a key component in Transformer architecture, used to provide position information for each position in a sequence. In natural language processing, commonly used position encodings include absolute position encoding and relative position encoding.

In computer vision tasks, position encoding is equally important. Traditional absolute position encodings such as sinusoidal position encoding have generalization problems when processing images of different sizes. Relative position encodings such as RPE (Relative Position Encoding) improve model generalization by modeling relative position relationships.

Rotary Position Embedding (RoPE) is a new type of position encoding method proposed in recent years. By encoding position information into the rotation angle of vectors, it maintains both the continuity of position information and good extrapolation ability, showing excellent performance in multiple tasks.

\section{Method}

\subsection{Overall Architecture}

The method proposed in this paper is based on the YOLOv10 architecture, replacing the original C2f modules with RCBFormerBlock modules to enhance the model's detection capability for small objects. The overall network architecture includes:

\begin{enumerate}
\item \textbf{Backbone Network}: Uses improved CSPDarknet as feature extractor, integrating RCBFormerBlock modules at key positions;
\item \textbf{Neck Network}: Uses FPN+PAN structure for multi-scale feature fusion;
\item \textbf{Detection Head}: Uses decoupled detection heads for separate classification and regression prediction.
\end{enumerate}

\begin{figure}[htbp]
\centerline{\includegraphics[width=\columnwidth]{architecture_simple.png}}
\caption{Network Architecture Overview}
\label{fig:architecture}
\end{figure}

\subsection{RCBFormerBlock Module Design}

RCBFormerBlock is the core innovation of this paper, integrating multiple advanced attention mechanisms and position encoding technologies. The module mainly contains the following four key components:

\subsubsection{Rotary Position Embedding (RoPE)}

Traditional position encoding methods have generalization problems when processing inputs of different sizes. This paper introduces Rotary Position Embedding (RoPE), which encodes position information into the rotation angles of Query and Key vectors:

\begin{equation}
\text{RoPE}(x, pos) = x \cdot \cos(pos \cdot \theta) + \text{rotate}(x) \cdot \sin(pos \cdot \theta)
\end{equation}

where $\theta$ is a predefined frequency vector, and $\text{rotate}(x)$ represents vector rotation operation. RoPE has the following advantages:
\begin{itemize}
\item Maintains continuity and differentiability of position information
\item Has good extrapolation ability, can handle sequence lengths not seen during training
\item High computational efficiency, does not add additional parameters
\end{itemize}

\subsubsection{Cross-Window Attention Mechanism}

To capture long-range dependencies while maintaining computational efficiency, this paper designs a cross-window attention mechanism. This mechanism divides the input feature map into multiple windows, performs self-attention computation within windows, and achieves cross-window information interaction through window shifting operations.

\subsubsection{Adaptive Channel Routing Calibration}

To improve the effectiveness of sparse attention, this paper introduces an adaptive channel routing calibration mechanism. This mechanism generates channel weights through global average pooling and lightweight convolutional networks to guide the attention computation process.

\subsubsection{RepMLP Feedforward Network}

To maintain expressive capability while improving inference efficiency, this paper uses RepMLP structure to replace traditional MLP. RepMLP uses multi-branch structure during training to enhance expressive capability, and can be reparameterized as a single convolution operation during inference.

\section{Experiments}

\subsection{Datasets and Evaluation Metrics}

This paper conducts experiments on two mainstream UAV aerial datasets:

\begin{enumerate}
\item \textbf{VisDrone Dataset}: Contains 10 categories of objects, with a total of 10,209 images, including 6,471 training images, 548 validation images, and 3,190 test images.
\item \textbf{UAVDT Dataset}: Focuses on vehicle detection, containing 40,209 images, including 23,258 training images, 5,830 validation images, and 11,121 test images.
\end{enumerate}

Evaluation metrics use COCO standards, including mAP@0.5, mAP@0.5:0.95, Precision, Recall, etc.

\subsection{Implementation Details}

\begin{itemize}
\item \textbf{Training Settings}: Use AdamW optimizer, initial learning rate 0.001, weight decay 0.0001
\item \textbf{Data Augmentation}: Use Mosaic, MixUp, random flip, random scaling and other data augmentation strategies
\item \textbf{Training Epochs}: 300 epochs, using cosine annealing learning rate scheduling
\item \textbf{Batch Size}: 16 (trained on 4 RTX 3090 GPUs)
\item \textbf{Input Size}: 640×640
\end{itemize}

\subsection{Ablation Study}

To verify the effectiveness of each component of RCBFormerBlock, this paper conducts detailed ablation experiments:

\begin{table}[htbp]
\caption{Ablation Study Results}
\begin{center}
\begin{tabular}{|c|c|c|c|c|c|}
\hline
\textbf{Method} & \textbf{RoPE} & \textbf{Cross-Win} & \textbf{Channel Cal} & \textbf{mAP@0.5} & \textbf{FPS} \\
\hline
Baseline & ✗ & ✗ & ✗ & 42.3 & 67.2 \\
\hline
+RoPE & ✓ & ✗ & ✗ & 43.1 & 65.8 \\
\hline
+Cross-Window & ✓ & ✓ & ✗ & 44.2 & 63.5 \\
\hline
+Channel Cal & ✓ & ✓ & ✓ & 44.8 & 62.9 \\
\hline
Full Method & ✓ & ✓ & ✓ & 45.5 & 61.4 \\
\hline
\end{tabular}
\label{tab:ablation}
\end{center}
\end{table}

Results show that each component contributes positively to the final performance, with the cross-window attention mechanism contributing the most.

\begin{figure}[htbp]
\centerline{\includegraphics[width=\columnwidth]{performance_comparison.png}}
\caption{Performance Comparison and Ablation Study Results}
\label{fig:performance}
\end{figure}

\subsection{Comparison with Existing Methods}

Comparison results with existing methods on the VisDrone dataset:

\begin{table}[htbp]
\caption{Comparison Results on VisDrone Dataset}
\begin{center}
\begin{tabular}{|c|c|c|c|c|}
\hline
\textbf{Method} & \textbf{mAP@0.5} & \textbf{mAP@0.5:0.95} & \textbf{Params(M)} & \textbf{FPS} \\
\hline
YOLOv5s & 39.8 & 23.1 & 7.2 & 74.3 \\
\hline
YOLOv8s & 42.3 & 25.6 & 11.2 & 67.2 \\
\hline
YOLOv10s & 43.1 & 26.2 & 7.8 & 69.5 \\
\hline
\textbf{Ours} & \textbf{45.5} & \textbf{27.8} & 8.9 & 61.4 \\
\hline
\end{tabular}
\label{tab:comparison_visdrone}
\end{center}
\end{table}

\begin{figure}[htbp]
\centerline{\includegraphics[width=\columnwidth]{size_analysis.png}}
\caption{Performance Analysis by Target Size}
\label{fig:size_analysis}
\end{figure}

\section{Results and Analysis}

\subsection{Quantitative Analysis}

Experimental results show that the RCBFormerBlock module proposed in this paper achieves significant performance improvements on both datasets:

\begin{enumerate}
\item \textbf{Detection Accuracy Improvement}: Compared to baseline YOLOv10s, mAP@0.5 improved by 2.4\% and 2.8\% on VisDrone and UAVDT datasets respectively;
\item \textbf{Small Object Detection Improvement}: For small objects with area less than 32² pixels, recall rate improved by 4.1\%;
\item \textbf{Computational Efficiency Balance}: Although FPS slightly decreased (about 8.7\%), it still meets real-time detection requirements.
\end{enumerate}

\subsection{Qualitative Analysis}

Through visualization analysis, we found:

\begin{enumerate}
\item \textbf{More Reasonable Attention Distribution}: RCBFormerBlock can more accurately locate small object regions and reduce background interference;
\item \textbf{Long-Range Dependency Modeling}: For dispersed small objects, the model can establish effective spatial associations;
\item \textbf{Enhanced Position Sensitivity}: RoPE encoding makes the model more sensitive to precise object positions.
\end{enumerate}

\begin{figure}[htbp]
\centerline{\includegraphics[width=\columnwidth]{attention_visualization.png}}
\caption{Attention Mechanism Visualization}
\label{fig:attention}
\end{figure}

\subsection{Error Analysis}

Analysis of detection failure cases found that main error types include:

\begin{enumerate}
\item \textbf{Extremely Small Object Missed Detection}: Objects with area less than 16² pixels are still prone to missed detection;
\item \textbf{Severe Occlusion Cases}: When objects are occluded more than 70\%, detection performance decreases significantly;
\item \textbf{Boundary Blur}: In image edges or poor lighting conditions, localization accuracy needs improvement.
\end{enumerate}

\begin{figure}[htbp]
\centerline{\includegraphics[width=\columnwidth]{error_analysis.png}}
\caption{Error Analysis and Performance under Different Conditions}
\label{fig:error_analysis}
\end{figure}

\section{Conclusion}

This paper proposes a fusion attention module RCBFormerBlock for the challenges of small object detection in UAV aerial images. The module significantly improves small object detection performance while maintaining computational efficiency by integrating rotary position embedding, cross-window attention, adaptive channel routing calibration, and RepMLP feedforward networks.

Extensive experiments on VisDrone and UAVDT datasets verify the effectiveness of the proposed method. Compared with existing methods, this paper's method achieves significant improvements in detection accuracy, especially excelling in small object detection.

Future work will continue to improve from the following directions:

\begin{enumerate}
\item \textbf{Further Optimize Computational Efficiency}: Explore more efficient attention mechanisms to reduce computational overhead;
\item \textbf{Enhance Extremely Small Object Detection Capability}: Combine super-resolution technology to improve detection performance of extremely small objects;
\item \textbf{Multi-modal Information Fusion}: Combine RGB, infrared and other multi-modal information to improve detection robustness in complex environments.
\end{enumerate}

\begin{thebibliography}{00}
\bibitem{b1} Cheng, H., Li, W., Zhang, M., et al. ``Improved YOLOv8-based UAV aerial small object detection algorithm,'' Computer Engineering and Applications, vol. 60, no. 8, pp. 123-130, 2024.

\bibitem{b2} Wang, Y., Liu, Y., Zhao, P., et al. ``SL-YOLO based aerial small object detection method,'' Optics and Precision Engineering, vol. 32, no. 5, pp. 1023-1032, 2024.

\bibitem{b3} Su, J., Lu, Y., Pan, S., et al. ``RoFormer: Enhanced transformer with rotary position embedding,'' Neurocomputing, vol. 568, pp. 127729, 2024.

\bibitem{b4} Liu, Z., Lin, Y., Cao, Y., et al. ``Swin transformer: Hierarchical vision transformer using shifted windows,'' Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 10012-10022, 2021.

\bibitem{b5} Zhu, L., Wang, X., Ke, Z., et al. ``BiFormer: Vision Transformer with Bi-Level Routing Attention,'' Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 10323-10333, 2023.

\bibitem{b6} Ding, X., Zhang, X., Han, J., et al. ``RepMLP: Re-parameterizing convolutions into fully-connected layers for efficient inference,'' Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 793-803, 2021.

\bibitem{b7} Zhu, P., Wen, L., Bian, X., et al. ``Vision meets drones: A challenge,'' arXiv preprint arXiv:1804.07437, 2018.

\bibitem{b8} Du, D., Qi, Y., Yu, H., et al. ``The unmanned aerial vehicle benchmark: Object detection and tracking,'' Proceedings of the European Conference on Computer Vision (ECCV), pp. 370-386, 2018.

\bibitem{b9} Lin, T. Y., Goyal, P., Girshick, R., et al. ``Focal loss for dense object detection,'' Proceedings of the IEEE International Conference on Computer Vision, pp. 2980-2988, 2017.

\bibitem{b10} Zheng, Z., Wang, P., Liu, W., et al. ``Distance-IoU loss: Faster and better learning for bounding box regression,'' Proceedings of the AAAI Conference on Artificial Intelligence, vol. 34, no. 07, pp. 12993-13000, 2020.
\end{thebibliography}

\end{document}
