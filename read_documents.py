#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> to read DOCX files and analyze paper structure
"""

import os
import sys
import zipfile
import xml.etree.ElementTree as ET

def read_docx_simple(file_path):
    """Simple DOCX reader without external dependencies"""
    try:
        with zipfile.ZipFile(file_path, 'r') as docx:
            # Read the main document
            xml_content = docx.read('word/document.xml')
            root = ET.fromstring(xml_content)

            # Extract text from paragraphs
            text = ""
            for paragraph in root.iter():
                if paragraph.tag.endswith('}t'):  # text elements
                    if paragraph.text:
                        text += paragraph.text
                elif paragraph.tag.endswith('}p'):  # paragraph elements
                    text += "\n"

            return text
    except Exception as e:
        print("Failed to read DOCX:", str(e))
        return None

def analyze_pdf_structure():
    """Analyze the structure of the reference PDF paper"""
    print("Based on the filename 'Efficient LoFTR: Semi-Dense Local Feature Matching with Sparse-Like Speed.pdf',")
    print("this appears to be a computer vision paper about feature matching.")
    print("\nTypical structure for such papers:")
    print("1. Abstract")
    print("2. Introduction")
    print("3. Related Work")
    print("4. Method/Approach")
    print("5. Experiments")
    print("6. Results")
    print("7. Conclusion")
    print("8. References")
    return True

def main():
    # Analyze PDF structure
    print("=" * 80)
    print("ANALYZING REFERENCE PAPER STRUCTURE")
    print("=" * 80)
    analyze_pdf_structure()

    # Read DOCX files
    docx_files = ["引言.docx", "融合式注意力模块RCBFormerBlock .docx"]

    for docx_file in docx_files:
        if os.path.exists(docx_file):
            print("\n" + "=" * 80)
            print("Reading DOCX file:", docx_file)
            print("=" * 80)

            docx_text = read_docx_simple(docx_file)
            if docx_text:
                print(docx_text)
            else:
                print("Cannot read DOCX file:", docx_file)
        else:
            print("DOCX file not found:", docx_file)

if __name__ == "__main__":
    main()
