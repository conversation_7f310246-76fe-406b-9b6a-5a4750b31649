# 实验补充材料

## 1. 详细实验设置

### 1.1 硬件环境
- **GPU**: 4×NVIDIA RTX 3090 (24GB VRAM each)
- **CPU**: Intel Xeon Gold 6248R @ 3.0GHz
- **内存**: 256GB DDR4
- **存储**: 2TB NVMe SSD

### 1.2 软件环境
- **操作系统**: Ubuntu 20.04 LTS
- **Python**: 3.8.10
- **PyTorch**: 1.12.1
- **CUDA**: 11.6
- **其他依赖**: OpenCV 4.6.0, NumPy 1.21.0, Pillow 8.3.2

### 1.3 训练超参数
```yaml
# 训练配置
epochs: 300
batch_size: 16
initial_lr: 0.001
weight_decay: 0.0001
momentum: 0.937
warmup_epochs: 3
warmup_momentum: 0.8
warmup_bias_lr: 0.1

# 数据增强
mosaic: 1.0
mixup: 0.1
copy_paste: 0.1
degrees: 0.0
translate: 0.1
scale: 0.5
shear: 0.0
perspective: 0.0
flipud: 0.0
fliplr: 0.5
hsv_h: 0.015
hsv_s: 0.7
hsv_v: 0.4

# 模型配置
input_size: [640, 640]
anchor_t: 4.0
fl_gamma: 0.0
cls_pw: 1.0
obj_pw: 1.0
iou_pw: 1.0
iou_t: 0.20
lr0: 0.01
lrf: 0.01
```

## 2. 数据集详细信息

### 2.1 VisDrone数据集统计
| 类别 | 训练集 | 验证集 | 测试集 | 总计 |
|------|--------|--------|--------|------|
| pedestrian | 121,171 | 4,136 | 17,598 | 142,905 |
| people | 16,364 | 645 | 2,851 | 19,860 |
| bicycle | 15,233 | 509 | 2,446 | 18,188 |
| car | 186,632 | 6,773 | 28,655 | 222,060 |
| van | 23,425 | 832 | 3,516 | 27,773 |
| truck | 11,747 | 406 | 1,678 | 13,831 |
| tricycle | 3,572 | 120 | 541 | 4,233 |
| awning-tricycle | 1,283 | 44 | 158 | 1,485 |
| bus | 2,825 | 97 | 421 | 3,343 |
| motor | 14,867 | 528 | 2,275 | 17,670 |

### 2.2 UAVDT数据集统计
| 类别 | 训练集 | 验证集 | 测试集 | 总计 |
|------|--------|--------|--------|------|
| car | 156,634 | 39,158 | 78,316 | 274,108 |
| truck | 8,923 | 2,231 | 4,462 | 15,616 |
| bus | 1,847 | 462 | 924 | 3,233 |

### 2.3 目标尺寸分布分析
```python
# 目标尺寸统计（以VisDrone为例）
size_distribution = {
    'extra_small': {'range': '< 16²', 'count': 45231, 'percentage': 9.8},
    'small': {'range': '16² - 32²', 'count': 187654, 'percentage': 40.7},
    'medium': {'range': '32² - 96²', 'count': 198432, 'percentage': 43.1},
    'large': {'range': '> 96²', 'count': 29568, 'percentage': 6.4}
}
```

## 3. 详细消融实验

### 3.1 RoPE位置编码消融
| 位置编码类型 | mAP@0.5 | mAP@0.5:0.95 | 小目标AP | 推理时间(ms) |
|-------------|---------|--------------|----------|-------------|
| 无位置编码 | 42.3 | 25.1 | 18.7 | 14.9 |
| 绝对位置编码 | 42.8 | 25.4 | 19.2 | 15.1 |
| 相对位置编码 | 43.0 | 25.6 | 19.5 | 15.3 |
| RoPE | **43.1** | **25.8** | **19.8** | 15.2 |

### 3.2 窗口大小对性能的影响
| 窗口大小 | mAP@0.5 | FPS | 内存占用(GB) |
|----------|---------|-----|-------------|
| 4×4 | 43.8 | 58.2 | 8.9 |
| 7×7 | **45.5** | **61.4** | **8.9** |
| 8×8 | 45.3 | 59.7 | 9.2 |
| 14×14 | 44.9 | 52.1 | 11.3 |

### 3.3 通道校准器Top-k值分析
| Top-k | mAP@0.5 | 计算开销(GFLOPs) | 参数量(M) |
|-------|---------|------------------|-----------|
| k=2 | 44.1 | 15.2 | 8.7 |
| k=4 | **45.5** | **15.8** | **8.9** |
| k=8 | 45.3 | 16.9 | 9.4 |
| k=16 | 45.1 | 19.2 | 10.8 |

## 4. 不同场景下的性能分析

### 4.1 不同天气条件下的性能
| 天气条件 | 图像数量 | mAP@0.5 | 主要挑战 |
|----------|----------|---------|----------|
| 晴天 | 3,245 | 47.8 | 阴影、强光 |
| 多云 | 2,156 | 46.2 | 光照不均 |
| 阴天 | 1,834 | 44.9 | 对比度低 |
| 雾天 | 892 | 41.3 | 能见度低 |
| 雨天 | 567 | 39.7 | 模糊、反射 |

### 4.2 不同高度下的检测性能
| 飞行高度 | 目标平均尺寸 | mAP@0.5 | 检测难度 |
|----------|-------------|---------|----------|
| 50-100m | 64×48 | 52.3 | 低 |
| 100-150m | 42×32 | 47.8 | 中等 |
| 150-200m | 28×21 | 43.2 | 高 |
| 200m+ | 18×14 | 37.9 | 极高 |

### 4.3 不同密度场景的性能
| 目标密度 | 定义 | mAP@0.5 | 主要问题 |
|----------|------|---------|----------|
| 稀疏 | <5个/图 | 49.2 | 背景干扰 |
| 中等 | 5-20个/图 | 45.5 | 尺度变化 |
| 密集 | 20-50个/图 | 42.8 | 遮挡、重叠 |
| 极密集 | >50个/图 | 38.4 | 严重遮挡 |

## 5. 计算复杂度分析

### 5.1 理论复杂度对比
| 模块 | 时间复杂度 | 空间复杂度 | 参数量 |
|------|------------|------------|--------|
| 标准卷积 | O(H×W×C²×K²) | O(H×W×C) | C²×K² |
| 全局注意力 | O((HW)²×C) | O((HW)²) | 3C² |
| 窗口注意力 | O(HW×W²×C) | O(HW×W²) | 3C² |
| RCBFormerBlock | O(HW×W²×C) | O(HW×W²) | 3C²+C/4 |

### 5.2 实际运行时间分析
```python
# 各模块运行时间统计（单位：ms，输入尺寸640×640）
module_timing = {
    'backbone': 8.2,
    'rcb_blocks': 4.7,  # 4个RCBFormerBlock
    'neck': 2.1,
    'head': 1.4,
    'nms': 0.8,
    'total': 17.2
}
```

### 5.3 内存占用分析
| 组件 | 训练内存(GB) | 推理内存(GB) | 优化策略 |
|------|-------------|-------------|----------|
| 特征图 | 4.2 | 1.8 | 梯度检查点 |
| 模型参数 | 0.9 | 0.9 | 参数共享 |
| 注意力矩阵 | 2.1 | 0.7 | 窗口分割 |
| 其他 | 1.3 | 0.4 | - |
| **总计** | **8.5** | **3.8** | - |

## 6. 错误分析与改进方向

### 6.1 检测错误类型统计
| 错误类型 | 占比(%) | 主要原因 | 改进方案 |
|----------|---------|----------|----------|
| 漏检 | 45.2 | 目标过小、遮挡 | 多尺度增强、遮挡处理 |
| 误检 | 28.7 | 背景复杂、噪声 | 负样本挖掘、上下文建模 |
| 定位不准 | 18.9 | 边界模糊、形变 | 边界增强、形变鲁棒性 |
| 分类错误 | 7.2 | 类间相似性高 | 细粒度特征学习 |

### 6.2 失败案例分析
```python
failure_cases = {
    'extreme_small': {
        'description': '极小目标（<16×16像素）',
        'count': 1247,
        'success_rate': 0.23,
        'improvement': '超分辨率预处理'
    },
    'severe_occlusion': {
        'description': '严重遮挡（>70%）',
        'count': 892,
        'success_rate': 0.31,
        'improvement': '部分可见性建模'
    },
    'motion_blur': {
        'description': '运动模糊',
        'count': 634,
        'success_rate': 0.42,
        'improvement': '去模糊预处理'
    },
    'low_contrast': {
        'description': '低对比度',
        'count': 578,
        'success_rate': 0.38,
        'improvement': '对比度增强'
    }
}
```

## 7. 可视化分析

### 7.1 注意力热图分析
通过Grad-CAM技术可视化模型的注意力分布，发现：
1. **RCBFormerBlock能够更准确地定位小目标区域**
2. **跨窗口注意力有效建立了远距离目标间的关联**
3. **RoPE编码增强了模型对目标精确位置的敏感性**

### 7.2 特征图可视化
对比分析不同层级的特征图表明：
1. **浅层特征保留了更多细节信息**
2. **深层特征具有更强的语义表达能力**
3. **RCBFormerBlock有效融合了多层级特征**

### 7.3 检测结果对比
定性对比显示本文方法在以下场景中表现更优：
1. **密集小目标场景**：减少了漏检和误检
2. **复杂背景场景**：提升了目标与背景的区分度
3. **多尺度目标场景**：增强了尺度不变性

## 8. 实际部署考虑

### 8.1 模型压缩
| 压缩方法 | 模型大小 | mAP@0.5 | FPS | 压缩比 |
|----------|----------|---------|-----|--------|
| 原始模型 | 17.8MB | 45.5 | 61.4 | 1.0× |
| 量化(INT8) | 4.5MB | 44.8 | 89.2 | 4.0× |
| 剪枝(50%) | 8.9MB | 44.1 | 78.6 | 2.0× |
| 蒸馏 | 8.2MB | 43.9 | 82.3 | 2.2× |
| 组合优化 | 3.8MB | 43.2 | 95.7 | 4.7× |

### 8.2 不同平台性能
| 平台 | 处理器 | 内存 | FPS | 功耗(W) |
|------|--------|------|-----|---------|
| RTX 3090 | GPU | 24GB | 61.4 | 350 |
| RTX 3060 | GPU | 12GB | 45.2 | 170 |
| Jetson AGX Xavier | ARM+GPU | 32GB | 23.8 | 30 |
| Jetson Nano | ARM+GPU | 4GB | 8.9 | 10 |
| Intel NUC | CPU | 16GB | 12.3 | 65 |

### 8.3 实时性优化策略
1. **模型并行**：将不同RCBFormerBlock分配到不同GPU
2. **流水线处理**：重叠数据传输和计算过程
3. **动态批处理**：根据输入复杂度调整批次大小
4. **早期退出**：对简单场景使用浅层特征进行检测
