#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终版本的注意力可视化脚本
"""

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np

def main():
    print("Generating attention visualization...")
    
    # 设置随机种子
    np.random.seed(42)
    
    # 创建图像
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # 图像尺寸
    size = 32
    
    # 1. 原始图像（模拟包含小目标）
    original_img = np.random.rand(size, size, 3) * 0.5 + 0.3
    
    # 添加小目标
    targets = [(8, 8), (20, 12), (15, 25), (5, 22)]
    for x, y in targets:
        if 0 <= x < size-3 and 0 <= y < size-3:
            original_img[y:y+3, x:x+3] = [1, 0, 0]  # 红色小目标
    
    axes[0, 0].imshow(original_img)
    axes[0, 0].set_title('Original Image\nwith Small Objects')
    axes[0, 0].axis('off')
    
    # 2. 基线注意力（较分散）
    baseline_attention = np.random.rand(size, size) * 0.4
    for x, y in targets:
        if 0 <= x < size and 0 <= y < size:
            # 创建较大的注意力区域
            for i in range(max(0, y-4), min(size, y+5)):
                for j in range(max(0, x-4), min(size, x+5)):
                    baseline_attention[i, j] += 0.3
    
    baseline_attention = np.clip(baseline_attention, 0, 1)
    
    im1 = axes[0, 1].imshow(baseline_attention, cmap='hot', alpha=0.8)
    axes[0, 1].imshow(original_img, alpha=0.3)
    axes[0, 1].set_title('Baseline Attention\n(Scattered)')
    axes[0, 1].axis('off')
    plt.colorbar(im1, ax=axes[0, 1], fraction=0.046, pad=0.04)
    
    # 3. 我们的方法注意力（更集中）
    our_attention = np.random.rand(size, size) * 0.2
    for x, y in targets:
        if 0 <= x < size and 0 <= y < size:
            # 创建更集中的注意力区域
            for i in range(max(0, y-2), min(size, y+3)):
                for j in range(max(0, x-2), min(size, x+3)):
                    our_attention[i, j] += 0.6
    
    # 添加目标间连接
    for i, (x1, y1) in enumerate(targets):
        for x2, y2 in targets[i+1:]:
            # 简单的线性连接
            steps = max(abs(x2-x1), abs(y2-y1))
            if steps > 0:
                for step in range(0, steps, 2):
                    t = step / float(steps)
                    x_interp = int(x1 + t * (x2 - x1))
                    y_interp = int(y1 + t * (y2 - y1))
                    if 0 <= x_interp < size and 0 <= y_interp < size:
                        our_attention[y_interp, x_interp] += 0.1
    
    our_attention = np.clip(our_attention, 0, 1)
    
    im2 = axes[0, 2].imshow(our_attention, cmap='hot', alpha=0.8)
    axes[0, 2].imshow(original_img, alpha=0.3)
    axes[0, 2].set_title('Our Method Attention\n(Focused & Connected)')
    axes[0, 2].axis('off')
    plt.colorbar(im2, ax=axes[0, 2], fraction=0.046, pad=0.04)
    
    # 4. RoPE位置编码
    pos_encoding = np.zeros((size, size))
    for i in range(size):
        for j in range(size):
            pos_encoding[i, j] = np.sin(i * 0.2) * np.cos(j * 0.2) * 0.5 + 0.5
    
    im3 = axes[1, 0].imshow(pos_encoding, cmap='viridis')
    axes[1, 0].set_title('RoPE Position Encoding\n(Rotary Pattern)')
    axes[1, 0].axis('off')
    plt.colorbar(im3, ax=axes[1, 0], fraction=0.046, pad=0.04)
    
    # 5. 跨窗口分割
    window_viz = np.zeros((size, size, 3))
    window_size = 8
    colors = [[1, 0.8, 0.8], [0.8, 1, 0.8], [0.8, 0.8, 1], [1, 1, 0.8]]
    
    color_idx = 0
    for i in range(0, size, window_size):
        for j in range(0, size, window_size):
            end_i = min(i + window_size, size)
            end_j = min(j + window_size, size)
            window_viz[i:end_i, j:end_j] = colors[color_idx % len(colors)]
            color_idx += 1
    
    # 添加边界
    for i in range(0, size, window_size):
        if i < size:
            window_viz[i, :] = [0.2, 0.2, 0.2]
    for j in range(0, size, window_size):
        if j < size:
            window_viz[:, j] = [0.2, 0.2, 0.2]
    
    axes[1, 1].imshow(window_viz)
    axes[1, 1].set_title('Cross-Window Partitioning\n(8x8 Windows)')
    axes[1, 1].axis('off')
    
    # 6. 通道校准权重
    channel_weights = np.random.rand(8, 8)
    # 让对角线更重要
    for i in range(8):
        channel_weights[i, i] *= 1.5
    # 让某些通道组合更重要
    channel_weights[1, 3] *= 1.8
    channel_weights[3, 1] *= 1.8
    channel_weights[5, 7] *= 1.8
    channel_weights[7, 5] *= 1.8
    
    # 归一化
    channel_weights = (channel_weights - channel_weights.min()) / (channel_weights.max() - channel_weights.min())
    
    im4 = axes[1, 2].imshow(channel_weights, cmap='plasma')
    axes[1, 2].set_title('Channel Calibration\nWeights')
    axes[1, 2].set_xlabel('Output Channels')
    axes[1, 2].set_ylabel('Input Channels')
    plt.colorbar(im4, ax=axes[1, 2], fraction=0.046, pad=0.04)
    
    plt.suptitle('RCBFormerBlock Attention Mechanism Visualization', 
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('attention_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Successfully generated: attention_visualization.png")
    
    # 创建性能对比图
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    # 注意力统计对比
    baseline_stats = [baseline_attention.max(), baseline_attention.mean(), 
                     baseline_attention.std(), np.sum(baseline_attention > 0.5) / baseline_attention.size]
    our_stats = [our_attention.max(), our_attention.mean(), 
                our_attention.std(), np.sum(our_attention > 0.5) / our_attention.size]
    
    metrics = ['Max Value', 'Mean Value', 'Std Dev', 'Focus Ratio']
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = ax.bar(x - width/2, baseline_stats, width, label='Baseline', color='lightcoral')
    bars2 = ax.bar(x + width/2, our_stats, width, label='RCBFormerBlock', color='skyblue')
    
    ax.set_xlabel('Metrics')
    ax.set_ylabel('Values')
    ax.set_title('Attention Statistics Comparison')
    ax.set_xticks(x)
    ax.set_xticklabels(metrics)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   '{:.3f}'.format(height), ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    plt.savefig('attention_stats_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("Successfully generated: attention_stats_comparison.png")
    print("Attention visualization completed!")

if __name__ == "__main__":
    main()
