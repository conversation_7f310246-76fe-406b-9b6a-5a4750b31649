#!/bin/bash

# 编译中文LaTeX论文的脚本
# 使用XeLaTeX编译器以支持中文

echo "开始编译中文论文..."

# 检查是否存在LaTeX文件
if [ ! -f "论文_中文完整版.tex" ]; then
    echo "错误: 找不到论文_中文完整版.tex文件"
    exit 1
fi

# 第一次编译
echo "第一次编译..."
xelatex -interaction=nonstopmode "论文_中文完整版.tex"

# 编译参考文献
echo "编译参考文献..."
bibtex "论文_中文完整版"

# 第二次编译
echo "第二次编译..."
xelatex -interaction=nonstopmode "论文_中文完整版.tex"

# 第三次编译（确保交叉引用正确）
echo "第三次编译..."
xelatex -interaction=nonstopmode "论文_中文完整版.tex"

# 清理临时文件
echo "清理临时文件..."
rm -f *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot

if [ -f "论文_中文完整版.pdf" ]; then
    echo "编译成功! 生成了 论文_中文完整版.pdf"
    echo "文件大小: $(du -h 论文_中文完整版.pdf | cut -f1)"
else
    echo "编译失败，请检查LaTeX文件中的错误"
    exit 1
fi

echo "编译完成!"
