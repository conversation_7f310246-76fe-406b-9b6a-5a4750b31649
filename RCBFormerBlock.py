import torch
import torch.nn as nn
from einops import rearrange
from timm.models.layers import DropPath
import torch.nn.functional as F

class RoPE(nn.Module):
    def __init__(self, dim):
        super().__init__()
        inv_freq = 1.0 / (10000 ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer("inv_freq", inv_freq)

    def forward(self, pos):
        sinusoid_inp = torch.ger(pos, self.inv_freq)
        sin, cos = sinusoid_inp.sin(), sinusoid_inp.cos()
        return sin, cos

def apply_rope(x, sin, cos):
    x1, x2 = x[..., ::2], x[..., 1::2]
    return torch.cat([x1 * cos - x2 * sin, x1 * sin + x2 * cos], dim=-1)


class CrossWindowAttention(nn.Module):
    def __init__(self, dim, heads, window_size=7, shift=False):
        super().__init__()
        self.dim = dim
        self.heads = heads
        self.head_dim = dim // heads
        self.scale = self.head_dim ** -0.5
        self.qkv = nn.Linear(dim, dim * 3, bias=False)
        self.proj = nn.Linear(dim, dim)
        self.window_size = window_size
        self.shift = shift

        # 初始化 RoPE
        self.rope = RoPE(self.head_dim)

    def forward(self, x):
        B, H, W, C = x.shape
        pad_h = (self.window_size - H % self.window_size) % self.window_size
        pad_w = (self.window_size - W % self.window_size) % self.window_size

        if pad_h > 0 or pad_w > 0:
            x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h))  # pad on H and W

        Hp, Wp = x.shape[1], x.shape[2]

        if self.shift:
            x = torch.roll(x, shifts=(-self.window_size // 2, -self.window_size // 2), dims=(1, 2))

        x_windows = rearrange(
            x, 'b (h ws1) (w ws2) c -> (b h w) (ws1 ws2) c',
            ws1=self.window_size, ws2=self.window_size
        )  # num_windows*B, N, C

        # 获取 token 的 1D 位置信息（RoPE 需要）
        pos_index = torch.arange(self.window_size * self.window_size, device=x.device)
        sin, cos = self.rope(pos_index)  # 生成旋转位置编码

        qkv = self.qkv(x_windows).reshape(x_windows.shape[0], x_windows.shape[1], 3, self.heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # 3, num_windows*B, heads, N, head_dim
        q, k, v = qkv[0], qkv[1], qkv[2]

        # 在 Q 和 K 上应用 RoPE
        q = apply_rope(q, sin, cos)
        k = apply_rope(k, sin, cos)

        attn = (q @ k.transpose(-2, -1)) * self.scale  # B*windows, heads, N, N
        attn = attn.softmax(dim=-1)
        out = (attn @ v).transpose(1, 2).reshape(x_windows.shape[0], x_windows.shape[1], C)
        out = self.proj(out)

        out = rearrange(
            out, '(b h w) (ws1 ws2) c -> b (h ws1) (w ws2) c',
            h=Hp // self.window_size,
            w=Wp // self.window_size,
            ws1=self.window_size,
            ws2=self.window_size
        )

        if self.shift:
            out = torch.roll(out, shifts=(self.window_size // 2, self.window_size // 2), dims=(1, 2))

        if pad_h > 0 or pad_w > 0:
            out = out[:, :H, :W, :]

        return out


# class CrossWindowAttention(nn.Module):
#     def __init__(self, dim, heads, window_size=7, shift=False):
#         super().__init__()
#         self.dim = dim
#         self.heads = heads
#         self.scale = (dim // heads) ** -0.5
#         self.qkv = nn.Linear(dim, dim * 3, bias=False)
#         self.proj = nn.Linear(dim, dim)
#         self.window_size = window_size
#         self.shift = shift

#     def forward(self, x):
#         B, H, W, C = x.shape
#         pad_h = (self.window_size - H % self.window_size) % self.window_size
#         pad_w = (self.window_size - W % self.window_size) % self.window_size

#         if pad_h > 0 or pad_w > 0:
#             x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h))  # pad on H and W

#         Hp, Wp = x.shape[1], x.shape[2]  # padded H and W

#         if self.shift:
#             x = torch.roll(x, shifts=(-self.window_size // 2, -self.window_size // 2), dims=(1, 2))

#         x_windows = rearrange(
#             x.contiguous(),
#             'b (h ws1) (w ws2) c -> (b h w) (ws1 ws2) c',
#             ws1=self.window_size, ws2=self.window_size
#         )

#         qkv = self.qkv(x_windows).reshape(x_windows.shape[0], x_windows.shape[1], 3, self.heads, C // self.heads)
#         qkv = qkv.permute(2, 0, 3, 1, 4).contiguous()
#         q, k, v = qkv[0], qkv[1], qkv[2]

#         attn = (q @ k.transpose(-2, -1)) * self.scale
#         attn = attn.softmax(dim=-1)
#         out = (attn @ v).transpose(1, 2).reshape(x_windows.shape[0], x_windows.shape[1], C)
#         out = self.proj(out)

#         out = rearrange(
#             out.contiguous(),
#             '(b h w) (ws1 ws2) c -> b (h ws1) (w ws2) c',
#             h=Hp // self.window_size,
#             w=Wp // self.window_size,
#             ws1=self.window_size,
#             ws2=self.window_size
#         )

#         if self.shift:
#             out = torch.roll(out, shifts=(self.window_size // 2, self.window_size // 2), dims=(1, 2))

#         if pad_h > 0 or pad_w > 0:
#             out = out[:, :H, :W, :]  # remove padding

#         return out


class ChannelCalibrator(nn.Module):
    def __init__(self, in_dim, topk):
        super().__init__()
        self.conv = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(in_dim, in_dim // 4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_dim // 4, topk, 1),
            nn.Sigmoid()
        )

    def forward(self, x):
        return self.conv(x)

class RepMLP(nn.Module):
    def __init__(self, dim, hidden_dim):
        super().__init__()
        self.fc1 = nn.Linear(dim, hidden_dim)
        self.dwconv = nn.Conv2d(hidden_dim, hidden_dim, 3, 1, 1, groups=hidden_dim)
        self.act = nn.GELU()
        self.fc2 = nn.Linear(hidden_dim, dim)

    def forward(self, x):
        B, H, W, C = x.shape
        x = self.fc1(x)
        x = rearrange(x, 'b h w c -> b c h w')
        x = self.dwconv(x)
        x = rearrange(x, 'b c h w -> b h w c')
        x = self.act(x)
        x = self.fc2(x)
        return x

class FBiFormerBlock(nn.Module):
    def __init__(self, dim, outdim, n_win=7, drop_path=0.1, num_heads=8, topk=4):
        super().__init__()
        self.pos_embed = nn.Conv2d(dim, dim, kernel_size=3, padding=1, groups=dim)
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        self.cross_attn = CrossWindowAttention(dim, heads=num_heads, window_size=n_win, shift=True)
        self.mlp = RepMLP(dim, hidden_dim=dim*4)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.gamma1 = nn.Parameter(torch.ones(dim))
        self.gamma2 = nn.Parameter(torch.ones(dim))

    def forward(self, x):
        # print(f"---------------[FBiFormerBlock] Input shape: {x.shape}---------------")  # 新增调试
        x = x + self.pos_embed(x)
        x = x.permute(0, 2, 3, 1)
        x = x + self.drop_path(self.gamma1 * self.cross_attn(self.norm1(x)))
        x = x + self.drop_path(self.gamma2 * self.mlp(self.norm2(x)))
        x = x.permute(0, 3, 1, 2)
        return x
