#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将HTML论文转换为PDF的脚本
使用weasyprint或者selenium+chrome来转换
"""

import os
import sys
import subprocess
from pathlib import Path

def check_weasyprint():
    """检查是否安装了weasyprint"""
    try:
        import weasyprint
        return True
    except ImportError:
        return False

def install_weasyprint():
    """尝试安装weasyprint"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "weasyprint"], check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def convert_with_weasyprint(html_file, pdf_file):
    """使用weasyprint转换HTML到PDF"""
    try:
        import weasyprint
        
        # 读取HTML文件
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 转换为PDF
        html_doc = weasyprint.HTML(string=html_content, base_url=str(Path(html_file).parent))
        html_doc.write_pdf(pdf_file)
        return True
    except Exception as e:
        print(f"WeasyPrint转换失败: {e}")
        return False

def convert_with_chrome():
    """使用Chrome浏览器的headless模式转换"""
    try:
        html_file = os.path.abspath("论文_中文HTML版本.html")
        pdf_file = os.path.abspath("论文_中文完整版.pdf")
        
        # 检查Chrome是否可用
        chrome_commands = [
            "google-chrome",
            "chromium-browser", 
            "chromium",
            "/usr/bin/google-chrome",
            "/usr/bin/chromium-browser"
        ]
        
        chrome_cmd = None
        for cmd in chrome_commands:
            try:
                subprocess.run([cmd, "--version"], capture_output=True, check=True)
                chrome_cmd = cmd
                break
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue
        
        if not chrome_cmd:
            print("未找到Chrome或Chromium浏览器")
            return False
        
        # 使用Chrome转换
        cmd = [
            chrome_cmd,
            "--headless",
            "--disable-gpu",
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--print-to-pdf=" + pdf_file,
            "--print-to-pdf-no-header",
            "--run-all-compositor-stages-before-draw",
            "--virtual-time-budget=5000",
            f"file://{html_file}"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0 and os.path.exists(pdf_file):
            print(f"成功使用Chrome转换: {pdf_file}")
            return True
        else:
            print(f"Chrome转换失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"Chrome转换出错: {e}")
        return False

def create_simple_pdf_instruction():
    """创建简单的PDF转换说明"""
    instruction = """
# 论文PDF转换说明

由于系统环境限制，无法直接转换为PDF。请使用以下方法之一：

## 方法1: 使用浏览器打印
1. 用浏览器打开 `论文_中文HTML版本.html`
2. 按 Ctrl+P (Windows/Linux) 或 Cmd+P (Mac)
3. 选择"保存为PDF"
4. 调整页面设置：
   - 纸张大小：A4
   - 边距：最小
   - 选项：背景图形
5. 保存为 `论文_中文完整版.pdf`

## 方法2: 使用在线转换工具
1. 访问 https://html-pdf-converter.com/ 或类似网站
2. 上传 `论文_中文HTML版本.html` 文件
3. 下载转换后的PDF

## 方法3: 使用命令行工具（如果有的话）
```bash
# 如果安装了wkhtmltopdf
wkhtmltopdf 论文_中文HTML版本.html 论文_中文完整版.pdf

# 如果安装了Chrome/Chromium
google-chrome --headless --disable-gpu --print-to-pdf=论文_中文完整版.pdf 论文_中文HTML版本.html
```

HTML版本已经包含了完整的论文内容，包括：
- 标准的学术格式
- 数学公式（MathJax渲染）
- 图表引用
- 参考文献
- 打印优化的CSS样式

转换后的PDF将保持专业的学术论文格式。
"""
    
    with open("PDF转换说明.md", "w", encoding="utf-8") as f:
        f.write(instruction)
    
    print("已创建 PDF转换说明.md 文件")

def main():
    print("开始转换HTML论文为PDF...")
    
    html_file = "论文_中文HTML版本.html"
    pdf_file = "论文_中文完整版.pdf"
    
    if not os.path.exists(html_file):
        print(f"错误: 找不到HTML文件 {html_file}")
        return False
    
    # 方法1: 尝试使用weasyprint
    if check_weasyprint():
        print("使用WeasyPrint转换...")
        if convert_with_weasyprint(html_file, pdf_file):
            print(f"成功转换为PDF: {pdf_file}")
            return True
    else:
        print("WeasyPrint未安装，尝试安装...")
        if install_weasyprint():
            print("WeasyPrint安装成功，开始转换...")
            if convert_with_weasyprint(html_file, pdf_file):
                print(f"成功转换为PDF: {pdf_file}")
                return True
    
    # 方法2: 尝试使用Chrome
    print("尝试使用Chrome浏览器转换...")
    if convert_with_chrome():
        return True
    
    # 如果都失败了，创建说明文件
    print("自动转换失败，创建手动转换说明...")
    create_simple_pdf_instruction()
    
    print("\n" + "="*60)
    print("HTML论文已准备就绪！")
    print(f"请打开浏览器访问: file://{os.path.abspath(html_file)}")
    print("然后使用浏览器的打印功能保存为PDF")
    print("详细说明请查看: PDF转换说明.md")
    print("="*60)
    
    return False

if __name__ == "__main__":
    main()
